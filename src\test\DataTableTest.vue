<template>
  <div class="p-4">
    <h2 class="text-xl mb-4">DataTable with n-virtual-list Test</h2>
    
    <!-- 测试大量数据的表格 -->
    <DataTable
      :columns="columns"
      :data="testData"
      height="400px"
      :item-size="62"
      @sort="handleSort"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import DataTable from '../components/shared/DataTable.vue'

// 定义表格列
const columns = ref([
  { key: 'id', title: 'ID', sortable: true, width: '80px', align: 'center' },
  { key: 'name', title: '姓名', sortable: true, width: '120px' },
  { key: 'age', title: '年龄', sortable: true, width: '80px', align: 'center' },
  { key: 'email', title: '邮箱', width: '200px' },
  { key: 'department', title: '部门', sortable: true, width: '120px' },
  { key: 'salary', title: '薪资', sortable: true, width: '100px', align: 'right' },
])

// 生成大量测试数据
const generateTestData = (count: number) => {
  const departments = ['技术部', '销售部', '市场部', '人事部', '财务部']
  const names = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十']
  
  return Array.from({ length: count }, (_, i) => ({
    id: i + 1,
    name: names[i % names.length] + (Math.floor(i / names.length) + 1),
    age: 20 + Math.floor(Math.random() * 40),
    email: `user${i + 1}@example.com`,
    department: departments[i % departments.length],
    salary: (5000 + Math.floor(Math.random() * 15000)).toLocaleString() + '元',
  }))
}

// 创建10000条测试数据来测试虚拟滚动性能
const testData = ref(generateTestData(10000))

// 处理排序
const handleSort = (column: any, order: 'asc' | 'desc' | null) => {
  console.log('排序:', column.key, order)
  
  if (!order) {
    // 重置为原始顺序
    testData.value = generateTestData(10000)
    return
  }
  
  testData.value.sort((a, b) => {
    const aValue = a[column.key]
    const bValue = b[column.key]
    
    // 数字类型排序
    if (column.key === 'id' || column.key === 'age') {
      const aNum = parseInt(aValue)
      const bNum = parseInt(bValue)
      return order === 'asc' ? aNum - bNum : bNum - aNum
    }
    
    // 字符串类型排序
    if (order === 'asc') {
      return aValue.localeCompare(bValue)
    } else {
      return bValue.localeCompare(aValue)
    }
  })
}
</script>
