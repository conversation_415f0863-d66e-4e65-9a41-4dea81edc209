import { defineStore } from 'pinia'
import { ref } from 'vue'

interface TableRowData {
  id: string
  name: string
  volt: string
  limit: string
  maxValue: string
  maxDiffValue: string
  totalOverTime: string
  longestOverTime: string
  maxValueTime: string
}

export const useSectionMonitoringStore = defineStore('sectionMonitoring', () => {
  const selectedTableRow = ref<TableRowData | null>(null)

  return { selectedTableRow }
})
